# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "281f3a151e9c2378ff1d34823f82f718"
name = "chat-bot"
handle = "chat-bot-39"
application_url = "https://collectables-agencies-parcel-acquired.trycloudflare.com"
embedded = true

[webhooks]
api_version = "2025-07"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_script_tags,write_products,write_script_tags"

[auth]
redirect_urls = ["https://collectables-agencies-parcel-acquired.trycloudflare.com/auth/callback", "https://collectables-agencies-parcel-acquired.trycloudflare.com/auth/shopify/callback", "https://collectables-agencies-parcel-acquired.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true
