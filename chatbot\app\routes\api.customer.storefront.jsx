import { authenticate } from "../shopify.server";

// Storefront API mutation to create customer access token
const CREATE_CUSTOMER_ACCESS_TOKEN = `
  mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
    customerAccessTokenCreate(input: $input) {
      customerAccessToken {
        accessToken
        expiresAt
      }
      customerUserErrors {
        field
        message
      }
    }
  }
`;

// Storefront API query to get customer data
const GET_CUSTOMER_QUERY = `
  query getCustomer($customerAccessToken: String!) {
    customer(customerAccessToken: $customerAccessToken) {
      id
      email
      firstName
      lastName
      phone
      acceptsMarketing
      createdAt
      updatedAt
      defaultAddress {
        id
        firstName
        lastName
        company
        address1
        address2
        city
        province
        country
        zip
        phone
      }
      addresses(first: 5) {
        edges {
          node {
            id
            firstName
            lastName
            company
            address1
            address2
            city
            province
            country
            zip
            phone
          }
        }
      }
      orders(first: 10) {
        edges {
          node {
            id
            orderNumber
            processedAt
            totalPrice {
              amount
              currencyCode
            }
            lineItems(first: 5) {
              edges {
                node {
                  title
                  quantity
                  variant {
                    title
                    price {
                      amount
                      currencyCode
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const action = async ({ request }) => {
  try {
    const { session, admin } = await authenticate.admin(request);
    
    if (!session?.shop) {
      return Response.json({ error: "No shop session found" }, { status: 401 });
    }

    const formData = await request.formData();
    const action = formData.get("action");
    const email = formData.get("email");
    const password = formData.get("password");
    const customerAccessToken = formData.get("customerAccessToken");

    // Get storefront access token from your app settings
    // You'll need to create a private app or use the Admin API to get this
    const storefrontAccessToken = process.env.SHOPIFY_STOREFRONT_ACCESS_TOKEN;
    
    if (!storefrontAccessToken) {
      return Response.json({ 
        error: "Storefront access token not configured. Please set SHOPIFY_STOREFRONT_ACCESS_TOKEN in your environment variables." 
      }, { status: 500 });
    }

    const shopDomain = session.shop;

    switch (action) {
      case "create_access_token":
        if (!email || !password) {
          return Response.json({ error: "Email and password are required" }, { status: 400 });
        }

        try {
          const response = await fetch(`https://${shopDomain}/api/2025-07/graphql.json`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Shopify-Storefront-Access-Token': storefrontAccessToken,
            },
            body: JSON.stringify({
              query: CREATE_CUSTOMER_ACCESS_TOKEN,
              variables: {
                input: {
                  email: email,
                  password: password
                }
              }
            })
          });

          const result = await response.json();

          if (result.data?.customerAccessTokenCreate?.customerUserErrors?.length > 0) {
            return Response.json({ 
              error: "Authentication failed", 
              details: result.data.customerAccessTokenCreate.customerUserErrors 
            }, { status: 401 });
          }

          const accessToken = result.data?.customerAccessTokenCreate?.customerAccessToken?.accessToken;
          const expiresAt = result.data?.customerAccessTokenCreate?.customerAccessToken?.expiresAt;

          if (!accessToken) {
            return Response.json({ error: "Failed to create access token" }, { status: 401 });
          }

          return Response.json({
            success: true,
            accessToken: accessToken,
            expiresAt: expiresAt
          });

        } catch (error) {
          console.error("Error creating customer access token:", error);
          return Response.json({ error: "Failed to authenticate customer" }, { status: 500 });
        }

      case "get_customer_data":
        if (!customerAccessToken) {
          return Response.json({ error: "Customer access token is required" }, { status: 400 });
        }

        try {
          const response = await fetch(`https://${shopDomain}/api/2025-07/graphql.json`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Shopify-Storefront-Access-Token': storefrontAccessToken,
            },
            body: JSON.stringify({
              query: GET_CUSTOMER_QUERY,
              variables: {
                customerAccessToken: customerAccessToken
              }
            })
          });

          const result = await response.json();

          if (result.errors) {
            return Response.json({ 
              error: "Failed to fetch customer data", 
              details: result.errors 
            }, { status: 400 });
          }

          const customer = result.data?.customer;

          if (!customer) {
            return Response.json({ error: "Customer not found or token expired" }, { status: 404 });
          }

          return Response.json({
            success: true,
            customer: customer
          });

        } catch (error) {
          console.error("Error fetching customer data:", error);
          return Response.json({ error: "Failed to fetch customer data" }, { status: 500 });
        }

      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }

  } catch (error) {
    console.error("Error in storefront customer API:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
};
