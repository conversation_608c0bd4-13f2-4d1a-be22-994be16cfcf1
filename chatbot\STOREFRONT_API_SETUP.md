# Shopify Storefront API Customer Authentication Setup

This guide explains how to set up and use the Shopify Storefront API for customer authentication and data fetching in your chatbot dashboard.

## 🚀 Quick Setup

### 1. Create Storefront Access Token

1. Go to your Shopify Admin: `https://your-shop.myshopify.com/admin`
2. Navigate to: **Apps** > **App and sales channel settings** > **Develop apps**
3. Click **"Create an app"** or select your existing app
4. Go to **"Configuration"** tab
5. In the **"Storefront API access scopes"** section, enable these scopes:
   - ✅ `unauthenticated_write_customers` (for customer login)
   - ✅ `unauthenticated_read_customers` (for customer data)
   - ✅ `unauthenticated_read_product_listings` (optional)
   - ✅ `unauthenticated_read_content` (optional)
6. Click **"Save"**
7. Go to **"API credentials"** tab
8. Copy the **"Storefront access token"**

### 2. Update Environment Variables

Add the Storefront Access Token to your `.env` file:

```env
SHOPIFY_STOREFRONT_ACCESS_TOKEN=your_storefront_access_token_here
```

### 3. Update App Scopes (if needed)

Make sure your app has the necessary Admin API scopes in `.env`:

```env
SCOPES=write_script_tags,read_script_tags,read_customers,read_customer_events
```

## 🔧 How It Works

### Customer Authentication Flow

1. **Customer Login**: Use the Storefront API to authenticate customers with email/password
2. **Access Token**: Get a customer access token for authenticated requests
3. **Fetch Data**: Use the access token to fetch comprehensive customer data
4. **Display**: Show customer information in your dashboard

### API Endpoints

#### 1. Customer Authentication (`/api/customer/storefront`)

**Create Access Token:**
```javascript
POST /api/customer/storefront
FormData: {
  action: "create_access_token",
  email: "<EMAIL>",
  password: "customer_password"
}
```

**Response:**
```json
{
  "success": true,
  "accessToken": "customer_access_token",
  "expiresAt": "2025-01-15T12:00:00Z"
}
```

**Fetch Customer Data:**
```javascript
POST /api/customer/storefront
FormData: {
  action: "get_customer_data",
  customerAccessToken: "customer_access_token"
}
```

**Response:**
```json
{
  "success": true,
  "customer": {
    "id": "gid://shopify/Customer/123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890",
    "acceptsMarketing": true,
    "orders": [...],
    "defaultAddress": {...}
  }
}
```

#### 2. Enhanced Dashboard Data (`/api/customers/dashboard`)

Now includes both local tracking data and Shopify customer data:

```json
{
  "success": true,
  "data": {
    "totalCustomers": 150,
    "totalCustomersFromShopify": 200,
    "recentCustomers": [...],
    "activeCustomers": [...],
    "allShopifyCustomers": [...],
    "stats": {
      "activeNow": 5,
      "recent24h": 25,
      "totalFromShopify": 200,
      "totalTracked": 150
    }
  }
}
```

## 📊 Dashboard Features

### New Customer Data Display

1. **Shopify Customer Count**: Total customers from Shopify Admin API
2. **Tracked Customer Count**: Customers who have interacted with your chatbot
3. **All Shopify Customers**: Latest customers with order history, phone, marketing preferences
4. **Enhanced Customer Cards**: Show order count, total spent, marketing status

### Customer Authentication Testing

1. Click **"Test Customer Auth"** button in dashboard
2. Enter customer email and password
3. View comprehensive customer data including:
   - Personal information
   - Order history
   - Default address
   - Marketing preferences

## 🔐 Security Notes

- **Storefront Access Token**: Keep this secure, never expose in client-side code
- **Customer Passwords**: Never store customer passwords, only use for authentication
- **Access Tokens**: Customer access tokens expire, handle expiration gracefully
- **Rate Limiting**: Shopify has rate limits, implement proper error handling

## 🛠️ Troubleshooting

### Common Issues

1. **"Storefront access token not configured"**
   - Make sure `SHOPIFY_STOREFRONT_ACCESS_TOKEN` is set in `.env`
   - Verify the token is correct from your Shopify Admin

2. **"Authentication failed"**
   - Check customer email/password are correct
   - Ensure customer account exists and is enabled
   - Verify Storefront API scopes are enabled

3. **"Customer not found or token expired"**
   - Customer access tokens expire after some time
   - Re-authenticate the customer to get a new token

4. **GraphQL errors**
   - Check the Storefront API version (currently using 2025-07)
   - Verify your GraphQL queries match the API schema

### Debug Mode

Enable debug logging by adding to your API routes:

```javascript
console.log('Storefront API Request:', {
  query: GET_CUSTOMER_QUERY,
  variables: { customerAccessToken }
});
```

## 📚 Resources

- [Shopify Storefront API Documentation](https://shopify.dev/docs/api/storefront)
- [Customer Authentication Guide](https://shopify.dev/docs/api/storefront/latest/mutations/customeraccesstokencreate)
- [GraphQL Admin API](https://shopify.dev/docs/api/admin-graphql)
- [Shopify App Development](https://shopify.dev/docs/apps)

## 🎯 Next Steps

1. Set up your Storefront Access Token
2. Test customer authentication in the dashboard
3. Monitor customer data in real-time
4. Customize the customer data display as needed
5. Implement additional Storefront API features (products, orders, etc.)
