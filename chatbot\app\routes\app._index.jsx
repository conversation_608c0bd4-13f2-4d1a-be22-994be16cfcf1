import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  List,
  Link,
  InlineStack,
  Badge,
  Banner,
  Spinner,
  DataTable,
  EmptyState,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { getScriptTags, getChatbotScriptUrl, ensureScriptTag, removeScriptTagsBySrc } from "../utils/scriptTags.server";

export const loader = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);

  try {
    // Get current script tags
    const scriptTags = await getScriptTags(admin);
    const appUrl = process.env.SHOPIFY_APP_URL;
    const chatbotScriptUrl = appUrl ? getChatbotScriptUrl(appUrl) : null;

    // Find our chatbot script tag
    const chatbotScriptTag = scriptTags.find(tag =>
      chatbotScriptUrl && tag.src === chatbotScriptUrl
    );

    // Get customer data from database
    let customerData = {
      totalCustomers: 0,
      recentCustomers: [],
      activeCustomers: []
    };

    try {
      const db = (await import("../db.server")).default;

      // Get total customers for this shop
      const totalCustomers = await db.customer.count({
        where: { shop: session.shop }
      });

      // Get recent customers (last 24 hours)
      const recentCustomers = await db.customer.findMany({
        where: {
          shop: session.shop,
          last_seen: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: { last_seen: 'desc' },
        take: 10,
        select: {
          shopify_customer_id: true,
          email: true,
          first_name: true,
          last_name: true,
          last_seen: true
        }
      });

      // Get currently active customers (last 5 minutes)
      const activeCustomers = await db.customer.findMany({
        where: {
          shop: session.shop,
          last_seen: {
            gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
          }
        },
        orderBy: { last_seen: 'desc' },
        select: {
          shopify_customer_id: true,
          email: true,
          first_name: true,
          last_name: true,
          last_seen: true
        }
      });

      customerData = {
        totalCustomers,
        recentCustomers,
        activeCustomers
      };
    } catch (dbError) {
      console.error("Error fetching customer data:", dbError);
    }

    return Response.json({
      shop: session.shop,
      scriptTags: scriptTags,
      chatbotScriptTag: chatbotScriptTag,
      chatbotScriptUrl: chatbotScriptUrl,
      appUrl: appUrl,
      isInstalled: !!chatbotScriptTag,
      customerData
    });
  } catch (error) {
    console.error("Error loading dashboard data:", error);
    return Response.json({
      shop: session.shop,
      scriptTags: [],
      chatbotScriptTag: null,
      chatbotScriptUrl: null,
      appUrl: process.env.SHOPIFY_APP_URL,
      isInstalled: false,
      error: error.message,
      customerData: {
        totalCustomers: 0,
        recentCustomers: [],
        activeCustomers: []
      }
    });
  }
};

export const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    const appUrl = process.env.SHOPIFY_APP_URL;
    if (!appUrl) {
      return Response.json({ error: "App URL not configured" }, { status: 500 });
    }

    const scriptUrl = getChatbotScriptUrl(appUrl);

    switch (action) {
      case "install_script":
        const scriptTag = await ensureScriptTag(admin, scriptUrl, "ONLINE_STORE");
        return Response.json({
          success: true,
          message: "Chatbot script tag installed successfully",
          scriptTag
        });

      case "uninstall_script":
        const removedCount = await removeScriptTagsBySrc(admin, scriptUrl);
        return Response.json({
          success: true,
          message: `Removed ${removedCount} chatbot script tag(s)`,
          removedCount
        });

      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in dashboard action:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
};

export default function ChatbotDashboard() {
  const data = useLoaderData();
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [customerData, setCustomerData] = useState(data.customerData);

  const isLoading = ["loading", "submitting"].includes(fetcher.state);

  // Function to fetch updated customer data
  const fetchCustomerData = async () => {
    try {
      const response = await fetch('/api/customers/dashboard');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCustomerData(result.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch customer data:', error);
    }
  };

  // Auto-refresh customer data every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Only refresh if not currently loading
      if (!isLoading && !isRefreshing) {
        fetchCustomerData();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, isLoading, isRefreshing]);

  useEffect(() => {
    if (fetcher.data?.success) {
      shopify.toast.show(fetcher.data.message);
      // Refresh the page data
      setIsRefreshing(true);
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else if (fetcher.data?.error) {
      shopify.toast.show(fetcher.data.error, { isError: true });
    }
  }, [fetcher.data, shopify]);

  const installScript = () => {
    fetcher.submit({ action: "install_script" }, { method: "POST" });
  };

  const uninstallScript = () => {
    fetcher.submit({ action: "uninstall_script" }, { method: "POST" });
  };

  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      await fetchCustomerData();
      shopify.toast.show("Customer data refreshed");
    } catch (error) {
      shopify.toast.show("Failed to refresh data", { isError: true });
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Page>
      <TitleBar title="Chatbot Dashboard" />

      {data.error && (
        <Banner status="critical" title="Error loading dashboard">
          <p>{data.error}</p>
        </Banner>
      )}

      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    🤖 Chatbot Status for {data.shop}
                  </Text>
                  <InlineStack gap="200" align="start">
                    <Badge
                      status={data.isInstalled ? "success" : "attention"}
                      size="medium"
                    >
                      {data.isInstalled ? "Active" : "Not Installed"}
                    </Badge>
                    {isRefreshing && <Spinner size="small" />}
                  </InlineStack>
                </BlockStack>

                {data.isInstalled ? (
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p" color="success">
                      ✅ Your chatbot is successfully installed and running on your storefront!
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-success"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border-success"
                    >
                      <BlockStack gap="200">
                        <Text as="h4" variant="headingSm">Script Tag Details:</Text>
                        <Text variant="bodySm">
                          <strong>Script URL:</strong> {data.chatbotScriptTag?.src}
                        </Text>
                        <Text variant="bodySm">
                          <strong>Display Scope:</strong> {data.chatbotScriptTag?.display_scope || 'online_store'}
                        </Text>
                        <Text variant="bodySm">
                          <strong>Created:</strong> {data.chatbotScriptTag?.created_at ? new Date(data.chatbotScriptTag.created_at).toLocaleString() : 'Unknown'}
                        </Text>
                      </BlockStack>
                    </Box>
                    <InlineStack gap="300">
                      <Button
                        variant="primary"
                        tone="critical"
                        loading={isLoading}
                        onClick={uninstallScript}
                      >
                        Uninstall Chatbot
                      </Button>
                      <Button
                        url={`https://${data.shop}`}
                        target="_blank"
                        variant="plain"
                      >
                        View Storefront
                      </Button>
                    </InlineStack>
                  </BlockStack>
                ) : (
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p">
                      Your chatbot is not currently installed on your storefront. Click the button below to install it.
                    </Text>
                    <Button
                      variant="primary"
                      loading={isLoading}
                      onClick={installScript}
                    >
                      Install Chatbot
                    </Button>
                  </BlockStack>
                )}
              </BlockStack>
            </Card>
          </Layout.Section>

          {/* Customer Data Section */}
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <InlineStack align="space-between">
                  <BlockStack gap="200">
                    <Text as="h2" variant="headingMd">
                      👥 Customer Identification Dashboard
                    </Text>
                    <Text variant="bodyMd" color="subdued">
                      Real-time customer data from your Shopify store
                      {customerData?.lastUpdated && (
                        <span> • Last updated: {new Date(customerData.lastUpdated).toLocaleTimeString()}</span>
                      )}
                    </Text>
                  </BlockStack>
                  <InlineStack gap="200">
                    <Button
                      variant="secondary"
                      size="slim"
                      loading={isRefreshing}
                      onClick={refreshData}
                    >
                      Refresh
                    </Button>
                    <Button
                      variant="plain"
                      size="slim"
                      pressed={autoRefresh}
                      onClick={() => setAutoRefresh(!autoRefresh)}
                    >
                      Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
                    </Button>
                  </InlineStack>
                </InlineStack>

                {customerData?.activeCustomers?.length > 0 && (
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingSm">
                      🟢 Currently Active Customers
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-success"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border-success"
                    >
                      <BlockStack gap="200">
                        {customerData.activeCustomers.map((customer, index) => (
                          <InlineStack key={index} align="space-between">
                            <BlockStack gap="050">
                              <Text variant="bodyMd">
                                {customer.first_name && customer.last_name
                                  ? `${customer.first_name} ${customer.last_name}`
                                  : customer.email || `Customer ${customer.shopify_customer_id}`
                                }
                              </Text>
                              {customer.email && (
                                <Text variant="bodySm" color="subdued">
                                  {customer.email}
                                </Text>
                              )}
                            </BlockStack>
                            <Text variant="bodySm" color="subdued">
                              {new Date(customer.last_seen).toLocaleTimeString()}
                            </Text>
                          </InlineStack>
                        ))}
                      </BlockStack>
                    </Box>
                  </BlockStack>
                )}

                {customerData?.recentCustomers?.length > 0 && (
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingSm">
                      📊 Recent Customer Activity (24h)
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-secondary"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                    >
                      <BlockStack gap="200">
                        {customerData.recentCustomers.slice(0, 8).map((customer, index) => (
                          <InlineStack key={index} align="space-between">
                            <BlockStack gap="050">
                              <Text variant="bodyMd">
                                {customer.first_name && customer.last_name
                                  ? `${customer.first_name} ${customer.last_name}`
                                  : customer.email || `Customer ${customer.shopify_customer_id}`
                                }
                              </Text>
                              {customer.email && (
                                <Text variant="bodySm" color="subdued">
                                  {customer.email}
                                </Text>
                              )}
                            </BlockStack>
                            <Text variant="bodySm" color="subdued">
                              {new Date(customer.last_seen).toLocaleString()}
                            </Text>
                          </InlineStack>
                        ))}
                        {customerData.recentCustomers.length > 8 && (
                          <Text variant="bodySm" color="subdued">
                            ... and {customerData.recentCustomers.length - 8} more
                          </Text>
                        )}
                      </BlockStack>
                    </Box>
                  </BlockStack>
                )}

                {(!customerData?.activeCustomers?.length && !customerData?.recentCustomers?.length) && (
                  <EmptyState
                    heading="No customer activity yet"
                    image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                  >
                    <p>Customer data will appear here when visitors interact with your chatbot.</p>
                  </EmptyState>
                )}
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    📊 Script Tags Overview
                  </Text>
                  <Text variant="bodyMd">
                    Total script tags: <strong>{data.scriptTags?.length || 0}</strong>
                  </Text>
                  {data.scriptTags?.length > 0 && (
                    <Box
                      padding="300"
                      background="bg-surface-secondary"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                    >
                      <BlockStack gap="100">
                        {data.scriptTags.slice(0, 5).map((tag, index) => (
                          <Text key={index} variant="bodySm">
                            • {tag.src?.split('/').pop() || 'Unknown script'}
                          </Text>
                        ))}
                        {data.scriptTags.length > 5 && (
                          <Text variant="bodySm" color="subdued">
                            ... and {data.scriptTags.length - 5} more
                          </Text>
                        )}
                      </BlockStack>
                    </Box>
                  )}
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    🔧 Configuration
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        App URL
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {data.appUrl ? new URL(data.appUrl).hostname : 'Not configured'}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Script URL
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {data.chatbotScriptUrl ? '/chatbot-loader.js' : 'Not available'}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Embed Script
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        /chatbot-embed.js
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    � Customer Activity
                  </Text>
                  <BlockStack gap="300">
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Total Customers
                      </Text>
                      <Badge status="info" size="medium">
                        {customerData?.totalCustomers || 0}
                      </Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Active Now
                      </Text>
                      <Badge status="success" size="medium">
                        {customerData?.activeCustomers?.length || 0}
                      </Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Recent (24h)
                      </Text>
                      <Badge status="attention" size="medium">
                        {customerData?.recentCustomers?.length || 0}
                      </Badge>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    �📚 Features
                  </Text>
                  <List>
                    <List.Item>
                      Automatic customer identification
                    </List.Item>
                    <List.Item>
                      Real-time chat interface
                    </List.Item>
                    <List.Item>
                      Mobile-responsive design
                    </List.Item>
                    <List.Item>
                      Easy installation & removal
                    </List.Item>
                  </List>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}