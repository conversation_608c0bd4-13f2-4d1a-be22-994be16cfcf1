import { authenticate } from "../shopify.server";
import db from "../db.server";

export const loader = async ({ request }) => {
  try {
    const { session } = await authenticate.admin(request);

    if (!session?.shop) {
      return Response.json({ error: "No shop session found" }, { status: 401 });
    }

    const shop = session.shop;

    try {
      // Get total customers for this shop
      const totalCustomers = await db.customer.count({
        where: { shop: shop }
      });

      // Get recent customers (last 24 hours)
      const recentCustomers = await db.customer.findMany({
        where: {
          shop: shop,
          last_seen: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: { last_seen: 'desc' },
        take: 20,
        select: {
          shopify_customer_id: true,
          email: true,
          first_name: true,
          last_name: true,
          last_seen: true,
          created_at: true
        }
      });

      // Get currently active customers (last 5 minutes)
      const activeCustomers = await db.customer.findMany({
        where: {
          shop: shop,
          last_seen: {
            gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
          }
        },
        orderBy: { last_seen: 'desc' },
        select: {
          shopify_customer_id: true,
          email: true,
          first_name: true,
          last_name: true,
          last_seen: true,
          created_at: true
        }
      });

      // Get customers from last hour for trend analysis
      const hourlyCustomers = await db.customer.findMany({
        where: {
          shop: shop,
          last_seen: {
            gte: new Date(Date.now() - 60 * 60 * 1000) // Last hour
          }
        },
        orderBy: { last_seen: 'desc' },
        select: {
          shopify_customer_id: true,
          last_seen: true
        }
      });

      return Response.json({
        success: true,
        data: {
          totalCustomers,
          recentCustomers,
          activeCustomers,
          hourlyCustomers,
          stats: {
            activeNow: activeCustomers.length,
            recent24h: recentCustomers.length,
            lastHour: hourlyCustomers.length
          },
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (dbError) {
      console.error(`Failed to fetch customer dashboard data for ${shop}:`, dbError);
      return Response.json({ 
        error: "Database error", 
        details: dbError.message 
      }, { status: 500 });
    }

  } catch (error) {
    console.error("Error in customer dashboard API:", error);
    return Response.json({ 
      error: "Internal server error", 
      details: error.message 
    }, { status: 500 });
  }
};
