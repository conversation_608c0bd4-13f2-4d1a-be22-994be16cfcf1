import { authenticate } from "../shopify.server";
import db from "../db.server";

// Shopify Admin API GraphQL query to get customers
const GET_CUSTOMERS_QUERY = `
  query getCustomers($first: Int!, $query: String) {
    customers(first: $first, query: $query, sortKey: UPDATED_AT, reverse: true) {
      edges {
        node {
          id
          email
          firstName
          lastName
          phone
          acceptsMarketing
          createdAt
          updatedAt
          lastOrderId
          ordersCount
          totalSpent
          state
          tags
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
      }
    }
  }
`;

export const loader = async ({ request }) => {
  try {
    const { session, admin } = await authenticate.admin(request);

    if (!session?.shop) {
      return Response.json({ error: "No shop session found" }, { status: 401 });
    }

    const shop = session.shop;

    try {
      // Fetch customers from Shopify Admin API
      let shopifyCustomers = [];
      let totalCustomersFromShopify = 0;

      try {
        const response = await admin.graphql(GET_CUSTOMERS_QUERY, {
          variables: {
            first: 50, // Get first 50 customers
            query: "state:ENABLED" // Only enabled customers
          }
        });

        const result = await response.json();
        if (result.data?.customers?.edges) {
          shopifyCustomers = result.data.customers.edges.map(edge => ({
            id: edge.node.id.replace('gid://shopify/Customer/', ''),
            email: edge.node.email,
            firstName: edge.node.firstName,
            lastName: edge.node.lastName,
            phone: edge.node.phone,
            acceptsMarketing: edge.node.acceptsMarketing,
            createdAt: edge.node.createdAt,
            updatedAt: edge.node.updatedAt,
            ordersCount: edge.node.ordersCount,
            totalSpent: edge.node.totalSpent,
            state: edge.node.state,
            tags: edge.node.tags
          }));
        }
      } catch (apiError) {
        console.error("Error fetching from Shopify API:", apiError);
      }

      // Get local database data for activity tracking
      const totalCustomers = await db.customer.count({
        where: { shop: shop }
      });

      // Get recent customers (last 24 hours) from local DB
      const recentCustomersDB = await db.customer.findMany({
        where: {
          shop: shop,
          last_seen: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: { last_seen: 'desc' },
        take: 20,
        select: {
          shopify_customer_id: true,
          email: true,
          first_name: true,
          last_name: true,
          last_seen: true,
          created_at: true
        }
      });

      // Get currently active customers (last 5 minutes) from local DB
      const activeCustomersDB = await db.customer.findMany({
        where: {
          shop: shop,
          last_seen: {
            gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
          }
        },
        orderBy: { last_seen: 'desc' },
        select: {
          shopify_customer_id: true,
          email: true,
          first_name: true,
          last_name: true,
          last_seen: true,
          created_at: true
        }
      });

      // Merge Shopify data with local activity data
      const enrichCustomerData = (localCustomers) => {
        return localCustomers.map(localCustomer => {
          const shopifyCustomer = shopifyCustomers.find(sc =>
            sc.id === localCustomer.shopify_customer_id
          );

          return {
            ...localCustomer,
            // Add Shopify data if available
            ...(shopifyCustomer && {
              phone: shopifyCustomer.phone,
              acceptsMarketing: shopifyCustomer.acceptsMarketing,
              ordersCount: shopifyCustomer.ordersCount,
              totalSpent: shopifyCustomer.totalSpent,
              tags: shopifyCustomer.tags,
              shopifyUpdatedAt: shopifyCustomer.updatedAt
            })
          };
        });
      };

      const recentCustomers = enrichCustomerData(recentCustomersDB);
      const activeCustomers = enrichCustomerData(activeCustomersDB);

      return Response.json({
        success: true,
        data: {
          totalCustomers,
          totalCustomersFromShopify: shopifyCustomers.length,
          recentCustomers,
          activeCustomers,
          allShopifyCustomers: shopifyCustomers.slice(0, 10), // First 10 for display
          stats: {
            activeNow: activeCustomers.length,
            recent24h: recentCustomers.length,
            totalFromShopify: shopifyCustomers.length,
            totalTracked: totalCustomers
          },
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (dbError) {
      console.error(`Failed to fetch customer dashboard data for ${shop}:`, dbError);
      return Response.json({ 
        error: "Database error", 
        details: dbError.message 
      }, { status: 500 });
    }

  } catch (error) {
    console.error("Error in customer dashboard API:", error);
    return Response.json({ 
      error: "Internal server error", 
      details: error.message 
    }, { status: 500 });
  }
};
